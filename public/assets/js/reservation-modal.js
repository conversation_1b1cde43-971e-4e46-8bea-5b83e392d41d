/**
 * Reservation Details Modal JavaScript
 *
 * Provides functionality for displaying reservation details in a modal overlay.
 *
 * Dependencies:
 * - Bootstrap 5 modal functionality
 * - CSRF token meta tag in page head
 *
 * Functions:
 * - openReservationModal(reservationId) - Opens modal and loads reservation data via AJAX
 * - renderReservationDetails(reservation) - Renders reservation data in modal content
 * - renderCostBreakdown(reservation) - Generates cost breakdown HTML
 * - updateModalFooter(reservation) - Updates modal footer with navigation buttons
 *
 * Usage:
 * Include this file after Bootstrap JS and call openReservationModal(id) to display a reservation.
 */

/**
 * Opens the reservation details modal and loads data via AJAX
 * @param {number} reservationId - The ID of the reservation to display
 */
function openReservationModal(reservationId) {
    const modal = new bootstrap.Modal(document.getElementById('reservationModal'));
    const modalLoading = document.getElementById('modalLoading');
    const modalError = document.getElementById('modalError');
    const modalContent = document.getElementById('modalContent');

    // Reset modal state
    modalLoading.style.display = 'block';
    modalError.classList.add('d-none');
    modalContent.classList.add('d-none');

    // Show modal
    modal.show();

    // Fetch reservation details
    fetch(`/reservations/${reservationId}/details`, {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        modalLoading.style.display = 'none';
        modalContent.classList.remove('d-none');
        renderReservationDetails(data);
        updateModalFooter(data);
    })
    .catch(error => {
        console.error('Error fetching reservation details:', error);
        modalLoading.style.display = 'none';
        modalError.classList.remove('d-none');
    });
}

/**
 * Renders reservation details in the modal content area
 * @param {Object} reservation - The reservation data object
 */
function renderReservationDetails(reservation) {
    const modalContent = document.getElementById('modalContent');
    const modalTitle = document.getElementById('reservationModalLabel');

    // Update modal title to match show.blade.php format
    modalTitle.innerHTML = `<i class="ti ti-calendar-event me-2"></i>Reservation #${reservation.id} Details
        <span class="badge bg-${reservation.status_color}-transparent text-${reservation.status_color} ms-2">
            ${reservation.status}
        </span>`;

    // Build the content HTML matching show.blade.php structure
    const contentHtml = `
        <div class="row gy-4">
            <!-- Reservation Schedule (matches show.blade.php col-xl-5) -->
            <div class="col-xl-5">
                <div class="card custom-card shadow-none border">
                    <div class="card-header">
                        <div class="card-title">Schedule</div>
                    </div>
                    <div class="card-body pb-0">
                        <div class="col-auto">
                            <div class="d-flex align-items-center mb-3">
                                <div class="me-3">
                                    <span class="avatar avatar-lg bg-dark-transparent">
                                        <i class="${reservation.field.icon || 'bx bx-stadium'}" style="font-size: 1.5rem;"></i>
                                    </span>
                                </div>
                                <div>
                                    <small class="text-muted">Field</small>
                                    <h5 class="mb-1">${reservation.field.name}</h5>
                                </div>
                            </div>

                            <div class="card-body">
                                <div class="row gy-3">
                                    <div class="col-6">
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-calendar me-2 text-primary"></i>
                                            <div>
                                                <small class="text-muted">Date</small>
                                                ${reservation.is_today ? '<small class="text-muted"> - </small><small class="text-primary">Today</small>' : ''}
                                                ${reservation.is_tomorrow ? '<small class="text-muted"> - </small><small class="text-primary">Tomorrow</small>' : ''}
                                                <div class="fw-semibold">${reservation.formatted_booking_date}</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-6">
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-clock me-2 text-info"></i>
                                            <div>
                                                <small class="text-muted">Time</small>
                                                <div class="fw-semibold">${reservation.time_range}</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-6">
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-user-plus me-2 text-success"></i>
                                            <div>
                                                <small class="text-muted">Reserved by member</small>
                                                <div class="fw-semibold">${reservation.user_name}</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-6">
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-hourglass me-2 text-warning"></i>
                                            <div>
                                                <small class="text-muted">Duration</small>
                                                <div class="fw-semibold">${reservation.duration_hours} ${reservation.duration_hours === 1 ? 'hour' : 'hours'}</div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Utilities -->
                                    ${reservation.utilities && reservation.utilities.length > 0 ? `
                                        <div class="col-6">
                                            <div class="d-flex align-items-center">
                                                <div>
                                                    <small class="text-muted" style="margin-left: 1.3rem;">Utilities</small>
                                                    ${reservation.utilities.map(utility => `
                                                        <div class="fw-semibold">
                                                            <i class="${utility.icon_class || 'ti ti-tool'} me-2 text-dark"></i>${utility.name} x ${utility.hours}
                                                        </div>
                                                    `).join('')}
                                                </div>
                                            </div>
                                        </div>
                                    ` : ''}

                                    ${reservation.special_requests ? `
                                        <div class="col-12">
                                            <div class="d-flex align-items-center">
                                                <i class="bx bx-message-star me-2 text-dark"></i>
                                                <div>
                                                    <small class="text-muted">Special Requests</small>
                                                    <div class="fw-semibold">${reservation.special_requests}</div>
                                                </div>
                                            </div>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reservation Cost (adjusted to col-xl-7 to fill remaining space) -->
            <div class="col-xl-7">
                <div class="card custom-card shadow-none border">
                    <div class="card-header">
                        <div class="card-title">Cost Breakdown</div>
                    </div>
                    <div class="card-body">
                        <div class="row gy-3">
                            <div class="col-12">
                                ${renderCostBreakdown(reservation)}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Information (matches show.blade.php col-xl-12) -->
            <div class="col-xl-12 mt-2">
                <div class="card custom-card shadow-none border">
                    <div class="card-header">
                        <div class="card-title">Customer Information</div>
                    </div>
                    <div class="card-body">
                        <div class="row gy-3">
                            <div class="col-md-4">
                                <small class="text-muted">Customer Name</small>
                                <div class="fw-semibold">${reservation.customer_display_name}</div>
                            </div>
                            <div class="col-md-4">
                                <small class="text-muted">Email</small>
                                <div class="fw-semibold">${reservation.customer_email || 'Not provided'}</div>
                            </div>
                            <div class="col-md-4">
                                <small class="text-muted">Phone</small>
                                <div class="fw-semibold">${reservation.customer_phone || 'Not provided'}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    modalContent.innerHTML = contentHtml;
}

/**
 * Renders the cost breakdown section for a reservation
 * @param {Object} reservation - The reservation data object
 * @returns {string} HTML string for the cost breakdown
 */
function renderCostBreakdown(reservation) {
    const costBreakdown = reservation.cost_breakdown;
    let html = `
        <div class="alert alert-success">
            <h6 class="fw-semibold mb-3">Reservation Cost Breakdown</h6>

            <!-- Field Cost Breakdown -->
            <div class="mb-3">
                <div class="fw-semibold mb-2">Field Cost:</div>
    `;

    // Check if we have rate breakdown data
    if (costBreakdown.rate_breakdown &&
        (costBreakdown.rate_breakdown.day_hours > 0 || costBreakdown.rate_breakdown.night_hours > 0)) {

        if (costBreakdown.rate_breakdown.day_hours > 0) {
            html += `
                <div class="fs-12 text-muted">
                    Day Rate: ${costBreakdown.rate_breakdown.day_hours} hours × XCG ${parseFloat(reservation.field.hourly_rate).toFixed(2)} = XCG ${parseFloat(costBreakdown.rate_breakdown.day_cost).toFixed(2)}
                </div>
            `;
        }

        if (costBreakdown.rate_breakdown.night_hours > 0) {
            html += `
                <div class="fs-12 text-muted">
                    Night Rate: ${costBreakdown.rate_breakdown.night_hours} hours × XCG ${parseFloat(reservation.field.night_hourly_rate).toFixed(2)} = XCG ${parseFloat(costBreakdown.rate_breakdown.night_cost).toFixed(2)}
                </div>
            `;
        }
    } else {
        // Simple rate display
        html += `
            <div class="fs-12 text-muted">
                ${reservation.duration_hours} hours × XCG ${parseFloat(reservation.field.hourly_rate).toFixed(2)} = XCG ${parseFloat(costBreakdown.subtotal).toFixed(2)}
            </div>
        `;
    }

    html += `
                <div class="fs-13 fw-semibold mt-1">
                    Field Total: XCG ${parseFloat(costBreakdown.subtotal).toFixed(2)}
                </div>
            </div>
    `;

    // Utility Cost Breakdown
    if (reservation.utilities.length > 0) {
        html += `
            <div class="mb-3">
                <div class="fw-semibold mb-2">Utility Costs:</div>
        `;

        reservation.utilities.forEach(utility => {
            html += `
                <div class="fs-12 text-muted">
                    ${utility.name}: ${utility.hours} hours × XCG ${parseFloat(utility.rate).toFixed(2)} = XCG ${parseFloat(utility.cost).toFixed(2)}
                </div>
            `;
        });

        html += `
                <div class="fs-13 fw-semibold mt-1">
                    Utility Total: XCG ${parseFloat(reservation.utility_total).toFixed(2)}
                </div>
            </div>
        `;
    }

    // Total Cost
    html += `
            <!-- Total Cost -->
            <div class="border-top pt-3">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <span class="fw-bold fs-16">Total Cost:</span>
                        <div class="fs-11 text-muted mt-1">
                            <i class="ti ti-shield-check me-1"></i>Calculated securely by server
                        </div>
                    </div>
                    <div class="h4 mb-0 text-success">
                        XCG ${parseFloat(reservation.total_cost).toFixed(2)}
                    </div>
                </div>
            </div>
        </div>
    `;

    return html;
}

/**
 * Updates the modal footer with action buttons based on user permissions
 * @param {Object} reservation - The reservation data object
 */
function updateModalFooter(reservation) {
    const modalFooter = document.getElementById('reservationModalFooter');

    // Build left-aligned action buttons
    let footerHtml = `<div class="d-flex gap-2">`;

    // Add View Reservation button (always available)
    footerHtml += `
        <a href="/reservations/${reservation.id}" class="btn btn-info">
            <i class="ti ti-eye me-1"></i>View Details
        </a>
    `;

    // Add Edit button if user can modify
    if (reservation.can_be_modified) {
        footerHtml += `
            <a href="${reservation.edit_url}" class="btn btn-warning">
                <i class="ti ti-edit me-1"></i>Edit Reservation
            </a>
        `;
    }

    footerHtml += `</div>`;

    modalFooter.innerHTML = footerHtml;
}



// Add timeline CSS styles to match show.blade.php template
// This ensures the timeline displays correctly in the modal
if (!document.getElementById('reservation-modal-timeline-styles')) {
    const timelineStyles = document.createElement('style');
    timelineStyles.id = 'reservation-modal-timeline-styles';
    timelineStyles.textContent = `
        #reservationModal .timeline {
            position: relative;
            padding-left: 30px;
        }

        #reservationModal .timeline::before {
            content: '';
            position: absolute;
            left: 10px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e9ecef;
        }

        #reservationModal .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }

        #reservationModal .timeline-marker {
            position: absolute;
            left: -25px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid #fff;
            box-shadow: 0 0 0 2px #e9ecef;
        }

        #reservationModal .timeline-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        #reservationModal .timeline-text {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 0;
        }

        /* Dark mode timeline styles */
        [data-theme-mode="dark"] #reservationModal .timeline::before {
            background: rgba(255,255,255,0.1);
        }

        [data-theme-mode="dark"] #reservationModal .timeline-marker {
            border-color: rgb(var(--light-rgb));
            box-shadow: 0 0 0 2px rgba(255,255,255,0.1);
        }

        [data-theme-mode="dark"] #reservationModal .timeline-text {
            color: rgba(255,255,255,0.6);
        }
    `;
    document.head.appendChild(timelineStyles);
}
