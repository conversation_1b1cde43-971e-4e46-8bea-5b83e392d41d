<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use App\Models\Utility;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Http\Controllers\ReservationController::class)]
class ReservationControllerTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Field $field;

    protected Utility $utility;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create(['role' => 'user']);

        // Create test field
        $this->field = Field::factory()->active()->create([
            'hourly_rate' => 50.00,
            'min_booking_hours' => 1,
            'max_booking_hours' => 4,
        ]);

        // Create test utility
        $this->utility = Utility::factory()->active()->create([
            'hourly_rate' => 10.00,
        ]);
    }

    #[Test]
    public function index_displays_user_reservations()
    {
        // Arrange
        Reservation::factory()->count(3)->forUser($this->user)->create();
        Reservation::factory()->count(2)->create(); // Other user's reservations

        // Act
        $response = $this->actingAs($this->user)->get(route('reservations.index'));

        // Assert
        $response->assertStatus(200);
        $response->assertViewIs('reservations.index');
        $response->assertViewHas('reservations');
        $response->assertViewHas('upcomingCount');
    }

    #[Test]
    public function index_calculates_upcoming_count_correctly()
    {
        // Arrange
        Reservation::factory()->forUser($this->user)->count(2)->create([
            'booking_date' => now()->addDays(2)->format('Y-m-d'),
            'status' => 'Confirmed',
        ]);
        Reservation::factory()->forUser($this->user)->create([
            'booking_date' => now()->subDay()->format('Y-m-d'),
            'status' => 'Completed',
        ]);
        Reservation::factory()->forUser($this->user)->create([
            'booking_date' => now()->addDays(3)->format('Y-m-d'),
            'status' => 'Cancelled',
        ]);

        // Act
        $response = $this->actingAs($this->user)->get(route('reservations.index'));

        // Assert
        $response->assertViewHas('upcomingCount', 2);
    }

    #[Test]
    public function index_requires_authentication()
    {
        // Act
        $response = $this->get(route('reservations.index'));

        // Assert
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function create_displays_form_with_fields_and_utilities()
    {
        // Arrange
        Field::factory()->active()->count(2)->create();
        Utility::factory()->active()->count(2)->create();

        // Act
        $response = $this->actingAs($this->user)->get(route('reservations.create'));

        // Assert
        $response->assertStatus(200);
        $response->assertViewIs('reservations.create');
        $response->assertViewHas('fields');
        $response->assertViewHas('utilities');
    }

    #[Test]
    public function create_pre_fills_form_when_field_id_provided()
    {
        // Act
        $response = $this->actingAs($this->user)
            ->get(route('reservations.create', ['field_id' => $this->field->id]));

        // Assert
        $response->assertStatus(200);
        $response->assertViewHas('selectedField', $this->field);
    }

    #[Test]
    public function create_requires_authentication()
    {
        // Act
        $response = $this->get(route('reservations.create'));

        // Assert
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function store_creates_reservation_with_valid_data()
    {
        // Arrange
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00',
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '************',
            'special_requests' => 'Need extra equipment',
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        // Assert
        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('bookings', [
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'duration_hours' => 2.0,
            'status' => 'Confirmed', // Auto-confirmed for Phase 1
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
        ]);
    }

    #[Test]
    public function store_creates_reservation_with_utilities()
    {
        // Arrange
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00',
            'utilities' => [
                ['id' => $this->utility->id, 'hours' => 1],
            ],
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        // Assert
        $response->assertRedirect();
        $response->assertSessionHas('success');

        $reservation = Reservation::where('user_id', $this->user->id)->first();
        $this->assertNotNull($reservation);

        // Check utility relationship
        $this->assertTrue($reservation->utilities->contains($this->utility));
    }

    #[Test]
    public function store_validates_required_fields()
    {
        // Act
        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), []);

        // Assert
        $response->assertSessionHasErrors([
            'field_id',
            'booking_date',
            'start_time',
            'end_time',
        ]);
    }

    #[Test]
    public function store_validates_utilities_when_provided()
    {
        // Arrange
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00',
            'utilities' => [
                ['id' => 999, 'hours' => 1], // Non-existent utility
                ['id' => $this->utility->id, 'hours' => 0], // Invalid hours
            ],
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        // Assert
        $response->assertSessionHasErrors(['utilities.0.id', 'utilities.1.hours']);
    }

    #[Test]
    public function store_requires_authentication()
    {
        // Act
        $response = $this->post(route('reservations.store'), []);

        // Assert
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function show_displays_reservation_for_owner()
    {
        // Arrange
        $reservation = Reservation::factory()->forUser($this->user)->create();

        // Act
        $response = $this->actingAs($this->user)
            ->get(route('reservations.show', $reservation));

        // Assert
        $response->assertStatus(200);
        $response->assertViewIs('reservations.show');
        $response->assertViewHas('reservation', $reservation);
    }

    #[Test]
    public function show_returns_403_for_unauthorized_user()
    {
        // Arrange
        $otherUser = User::factory()->create();
        $reservation = Reservation::factory()->forUser($otherUser)->create();

        // Act
        $response = $this->actingAs($this->user)
            ->get(route('reservations.show', $reservation));

        // Assert
        $response->assertStatus(403);
    }

    #[Test]
    public function show_requires_authentication()
    {
        // Arrange
        $reservation = Reservation::factory()->create();

        // Act
        $response = $this->get(route('reservations.show', $reservation));

        // Assert
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function edit_displays_form_for_modifiable_reservation()
    {
        // Arrange
        $reservation = Reservation::factory()->forUser($this->user)->create([
            'booking_date' => now()->addDays(2)->format('Y-m-d'), // More than 24 hours ahead
            'status' => 'Confirmed',
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->get(route('reservations.edit', $reservation));

        // Assert
        $response->assertStatus(200);
        $response->assertViewIs('reservations.edit');
        $response->assertViewHas('reservation', $reservation);
        $response->assertViewHas('fields');
        $response->assertViewHas('utilities');
    }

    #[Test]
    public function edit_returns_403_for_unauthorized_user()
    {
        // Arrange
        $otherUser = User::factory()->create();
        $reservation = Reservation::factory()->forUser($otherUser)->create();

        // Act
        $response = $this->actingAs($this->user)
            ->get(route('reservations.edit', $reservation));

        // Assert
        $response->assertStatus(403);
    }

    #[Test]
    public function edit_redirects_for_non_modifiable_reservation()
    {
        // Arrange - Create reservation within 24-hour cutoff
        $reservation = Reservation::factory()->forUser($this->user)->create([
            'booking_date' => now()->addHours(12)->format('Y-m-d'),
            'status' => 'Confirmed',
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->get(route('reservations.edit', $reservation));

        // Assert
        $response->assertRedirect();
        $response->assertSessionHas('error');
    }

    #[Test]
    public function edit_requires_authentication()
    {
        // Arrange
        $reservation = Reservation::factory()->create();

        // Act
        $response = $this->get(route('reservations.edit', $reservation));

        // Assert
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function update_modifies_reservation_with_valid_data()
    {
        // Arrange
        $reservation = Reservation::factory()->forUser($this->user)->create([
            'booking_date' => now()->addDays(3)->format('Y-m-d'), // More than 24 hours ahead
            'field_id' => $this->field->id,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
        ]);

        $updateData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(4)->format('Y-m-d'),
            'start_time' => '14:00',
            'end_time' => '17:00',
            'customer_name' => 'Updated Name',
            'special_requests' => 'Updated requests',
            'utilities' => [
                ['id' => $this->utility->id, 'hours' => 2],
            ],
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->put(route('reservations.update', $reservation), $updateData);

        // Assert
        $response->assertRedirect(route('reservations.show', $reservation));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('bookings', [
            'id' => $reservation->id,
            'start_time' => '14:00',
            'end_time' => '17:00',
            'duration_hours' => 3.0,
            'customer_name' => 'Updated Name',
            'special_requests' => 'Updated requests',
        ]);
    }

    #[Test]
    public function update_returns_403_for_unauthorized_user()
    {
        // Arrange
        $otherUser = User::factory()->create();
        $reservation = Reservation::factory()->forUser($otherUser)->create();

        // Act
        $response = $this->actingAs($this->user)
            ->put(route('reservations.update', $reservation), ['field_id' => $this->field->id]);

        // Assert
        $response->assertStatus(403);
    }

    #[Test]
    public function update_redirects_for_non_modifiable_reservation()
    {
        // Arrange - Create reservation within 24-hour cutoff
        $reservation = Reservation::factory()->forUser($this->user)->create([
            'booking_date' => now()->addHours(12)->format('Y-m-d'),
            'status' => 'Confirmed',
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->put(route('reservations.update', $reservation), ['field_id' => $this->field->id]);

        // Assert
        $response->assertRedirect();
        $response->assertSessionHas('error');
    }

    #[Test]
    public function update_validates_required_fields()
    {
        // Arrange
        $reservation = Reservation::factory()->forUser($this->user)->create([
            'booking_date' => now()->addDays(3)->format('Y-m-d'),
            'status' => 'Confirmed',
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->put(route('reservations.update', $reservation), []);

        // Assert
        $response->assertSessionHasErrors([
            'field_id',
            'booking_date',
            'start_time',
            'end_time',
        ]);
    }

    #[Test]
    public function update_requires_authentication()
    {
        // Arrange
        $reservation = Reservation::factory()->create();

        // Act
        $response = $this->put(route('reservations.update', $reservation), []);

        // Assert
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function cancel_updates_reservation_status_for_owner()
    {
        // Arrange
        $reservation = Reservation::factory()->forUser($this->user)->create([
            'booking_date' => now()->addDays(2)->format('Y-m-d'), // More than 24 hours ahead
            'status' => 'Confirmed',
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->post(route('reservations.cancel', $reservation));

        // Assert
        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('bookings', [
            'id' => $reservation->id,
            'status' => 'Cancelled',
        ]);

        $reservation->refresh();
        $this->assertNotNull($reservation->cancelled_at);
    }

    #[Test]
    public function cancel_returns_403_for_unauthorized_user()
    {
        // Arrange
        $otherUser = User::factory()->create();
        $reservation = Reservation::factory()->forUser($otherUser)->create();

        // Act
        $response = $this->actingAs($this->user)
            ->post(route('reservations.cancel', $reservation));

        // Assert
        $response->assertStatus(403);

        $this->assertDatabaseHas('bookings', [
            'id' => $reservation->id,
            'status' => $reservation->status, // Original status unchanged
        ]);
    }

    #[Test]
    public function cancel_redirects_for_non_cancellable_reservation()
    {
        // Arrange - Create reservation within 24-hour cutoff
        $reservation = Reservation::factory()->forUser($this->user)->create([
            'booking_date' => now()->addHours(12)->format('Y-m-d'),
            'status' => 'Confirmed',
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->post(route('reservations.cancel', $reservation));

        // Assert
        $response->assertRedirect();
        $response->assertSessionHas('error');

        $this->assertDatabaseHas('bookings', [
            'id' => $reservation->id,
            'status' => $reservation->status, // Original status unchanged
        ]);
    }

    #[Test]
    public function cancel_redirects_for_already_cancelled_reservation()
    {
        // Arrange
        $reservation = Reservation::factory()->forUser($this->user)->create([
            'status' => 'Cancelled',
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->post(route('reservations.cancel', $reservation));

        // Assert
        $response->assertRedirect();
        $response->assertSessionHas('error');
    }

    #[Test]
    public function cancel_requires_authentication()
    {
        // Arrange
        $reservation = Reservation::factory()->create();

        // Act
        $response = $this->post(route('reservations.cancel', $reservation));

        // Assert
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function cancel_returns_json_response_for_ajax_request()
    {
        // Arrange
        $reservation = Reservation::factory()->forUser($this->user)->create([
            'booking_date' => now()->addDays(2)->format('Y-m-d'), // More than 24 hours ahead
            'status' => 'Confirmed',
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.cancel', $reservation));

        // Assert
        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'message' => 'Reservation cancelled successfully.',
            'reservation' => [
                'id' => $reservation->id,
                'status' => 'Cancelled',
                'status_color' => 'danger',
                'can_be_cancelled' => false,
                'can_be_uncancelled' => true,
                'can_be_modified' => false,
                'user_can_edit' => true,
            ],
        ]);

        $this->assertDatabaseHas('bookings', [
            'id' => $reservation->id,
            'status' => 'Cancelled',
        ]);
    }

    #[Test]
    public function cancel_returns_json_error_for_ajax_request_when_unauthorized()
    {
        // Arrange
        $otherUser = User::factory()->create();
        $reservation = Reservation::factory()->forUser($otherUser)->create();

        // Act
        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.cancel', $reservation));

        // Assert
        $response->assertStatus(403);
        $response->assertJson([
            'error' => 'You can only cancel your own reservations.',
        ]);
    }

    #[Test]
    public function cancel_returns_json_error_for_ajax_request_when_not_cancellable()
    {
        // Arrange - Create reservation within 24-hour cutoff
        $reservation = Reservation::factory()->forUser($this->user)->create([
            'booking_date' => now()->addHours(12)->format('Y-m-d'),
            'status' => 'Confirmed',
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.cancel', $reservation));

        // Assert
        $response->assertStatus(422);
        $response->assertJson([
            'error' => 'This reservation cannot be cancelled. Reservations can only be cancelled up to 24 hours before the scheduled time.',
        ]);
    }

    #[Test]
    public function uncancel_updates_reservation_status_for_owner()
    {
        // Arrange
        $reservation = Reservation::factory()->forUser($this->user)->create([
            'booking_date' => now()->addDays(2)->format('Y-m-d'), // Future date
            'status' => 'Cancelled',
            'cancelled_at' => now(),
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->post(route('reservations.uncancel', $reservation));

        // Assert
        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('bookings', [
            'id' => $reservation->id,
            'status' => 'Confirmed',
            'cancelled_at' => null,
        ]);
    }

    #[Test]
    public function uncancel_returns_json_response_for_ajax_request()
    {
        // Arrange
        $reservation = Reservation::factory()->forUser($this->user)->create([
            'booking_date' => now()->addDays(2)->format('Y-m-d'), // Future date
            'status' => 'Cancelled',
            'cancelled_at' => now(),
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.uncancel', $reservation));

        // Assert
        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'message' => 'Reservation restored successfully.',
            'reservation' => [
                'id' => $reservation->id,
                'status' => 'Confirmed',
                'status_color' => 'secondary',
                'can_be_cancelled' => true,
                'can_be_uncancelled' => false,
                'can_be_modified' => true,
                'user_can_edit' => true,
            ],
        ]);

        $this->assertDatabaseHas('bookings', [
            'id' => $reservation->id,
            'status' => 'Confirmed',
            'cancelled_at' => null,
        ]);
    }

    #[Test]
    public function uncancel_returns_json_error_for_ajax_request_when_unauthorized()
    {
        // Arrange
        $otherUser = User::factory()->create();
        $reservation = Reservation::factory()->forUser($otherUser)->create([
            'status' => 'Cancelled',
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.uncancel', $reservation));

        // Assert
        $response->assertStatus(403);
        $response->assertJson([
            'error' => 'You can only restore your own reservations.',
        ]);
    }

    #[Test]
    public function uncancel_returns_json_error_for_ajax_request_when_not_cancelled()
    {
        // Arrange
        $reservation = Reservation::factory()->forUser($this->user)->create([
            'status' => 'Confirmed', // Not cancelled
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.uncancel', $reservation));

        // Assert
        $response->assertStatus(422);
        $response->assertJson([
            'error' => 'This reservation is not cancelled.',
        ]);
    }

    #[Test]
    public function check_availability_returns_slots_excluding_specified_reservation()
    {
        // Arrange
        $this->actingAs($this->user);
        $date = now()->addDays(1)->format('Y-m-d');

        // Create existing reservation
        $existingReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
        ]);

        // Act - Check availability without exclude parameter
        $responseWithoutExclude = $this->postJson(route('reservations.check-availability'), [
            'field_id' => $this->field->id,
            'date' => $date,
            'duration_hours' => 2,
        ]);

        // Act - Check availability with exclude parameter
        $responseWithExclude = $this->postJson(route('reservations.check-availability'), [
            'field_id' => $this->field->id,
            'date' => $date,
            'duration_hours' => 2,
            'exclude_reservation_id' => $existingReservation->id,
        ]);

        // Assert - Without exclude, the 10:00 slot should not be available
        $responseWithoutExclude->assertOk();
        $slotsWithoutExclude = $responseWithoutExclude->json('slots');
        $conflictingSlots = array_filter($slotsWithoutExclude, function ($slot) {
            return $slot['start_time'] === '10:00';
        });
        $this->assertEmpty($conflictingSlots, 'Slot should not be available without exclude parameter');

        // Assert - With exclude, the 10:00 slot should be available
        $responseWithExclude->assertOk();
        $slotsWithExclude = $responseWithExclude->json('slots');
        $availableSlots = array_filter($slotsWithExclude, function ($slot) {
            return $slot['start_time'] === '10:00';
        });
        $this->assertNotEmpty($availableSlots, 'Slot should be available when excluding the reservation');

        // Verify response structure
        $responseWithExclude->assertJsonStructure([
            'available',
            'message',
            'slots' => [
                '*' => [
                    'start_time',
                    'end_time',
                    'display',
                    'value',
                ],
            ],
        ]);
    }

    // ========================================
    // INDEX METHOD FILTERING TESTS
    // ========================================

    #[Test]
    public function index_filters_by_status()
    {
        // Arrange
        Reservation::factory()->forUser($this->user)->create(['status' => 'Confirmed']);
        Reservation::factory()->forUser($this->user)->create(['status' => 'Pending']);
        Reservation::factory()->forUser($this->user)->create(['status' => 'Cancelled']);

        // Act
        $response = $this->actingAs($this->user)
            ->get(route('reservations.index', ['status' => 'Confirmed']));

        // Assert
        $response->assertStatus(200);
        $response->assertViewHas('reservations');
        $reservations = $response->viewData('reservations');
        $this->assertEquals(1, $reservations->total());
        $this->assertEquals('Confirmed', $reservations->first()->status);
    }

    #[Test]
    public function index_filters_by_date_range()
    {
        // Arrange
        $startDate = now()->addDays(1)->format('Y-m-d');
        $endDate = now()->addDays(3)->format('Y-m-d');

        Reservation::factory()->forUser($this->user)->create([
            'booking_date' => now()->format('Y-m-d'), // Before range
        ]);
        Reservation::factory()->forUser($this->user)->create([
            'booking_date' => now()->addDays(2)->format('Y-m-d'), // Within range
        ]);
        Reservation::factory()->forUser($this->user)->create([
            'booking_date' => now()->addDays(5)->format('Y-m-d'), // After range
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->get(route('reservations.index', [
                'date_from' => $startDate,
                'date_to' => $endDate,
            ]));

        // Assert
        $response->assertStatus(200);
        $reservations = $response->viewData('reservations');
        $this->assertEquals(1, $reservations->total());
    }

    #[Test]
    public function index_filters_by_field()
    {
        // Arrange
        $otherField = Field::factory()->active()->create();

        Reservation::factory()->forUser($this->user)->create(['field_id' => $this->field->id]);
        Reservation::factory()->forUser($this->user)->create(['field_id' => $this->field->id]);
        Reservation::factory()->forUser($this->user)->create(['field_id' => $otherField->id]);

        // Act
        $response = $this->actingAs($this->user)
            ->get(route('reservations.index', ['field_id' => $this->field->id]));

        // Assert
        $response->assertStatus(200);
        $reservations = $response->viewData('reservations');
        $this->assertEquals(2, $reservations->total());
        $reservations->each(function ($reservation) {
            $this->assertEquals($this->field->id, $reservation->field_id);
        });
    }

    #[Test]
    public function index_shows_all_reservations_for_all_users()
    {
        // Arrange
        $otherUser = User::factory()->create();
        Reservation::factory()->forUser($this->user)->count(2)->create();
        Reservation::factory()->forUser($otherUser)->count(3)->create();

        // Act
        $response = $this->actingAs($this->user)->get(route('reservations.index'));

        // Assert
        $response->assertStatus(200);
        $reservations = $response->viewData('reservations');
        $this->assertEquals(5, $reservations->total()); // All reservations visible
    }

    #[Test]
    public function index_includes_fields_in_view_data()
    {
        // Arrange
        Field::factory()->active()->count(3)->create();

        // Act
        $response = $this->actingAs($this->user)->get(route('reservations.index'));

        // Assert
        $response->assertStatus(200);
        $response->assertViewHas('fields');
        $fields = $response->viewData('fields');
        $this->assertGreaterThanOrEqual(3, $fields->count());
    }

    // ========================================
    // AJAX/JSON ENDPOINT TESTS
    // ========================================

    #[Test]
    public function show_returns_json_when_ajax_request()
    {
        // Arrange
        $reservation = Reservation::factory()->forUser($this->user)->create();

        // Act
        $response = $this->actingAs($this->user)
            ->getJson(route('reservations.show', $reservation));

        // Assert
        $response->assertOk();
        $response->assertJsonStructure([
            'id',
            'status',
            'status_color',
            'can_be_modified',
            'can_be_cancelled',
            'field' => [
                'id',
                'name',
                'type',
                'capacity',
                'hourly_rate',
                'night_hourly_rate',
                'description',
            ],
            'booking_date',
            'time_range',
            'duration_hours',
            'formatted_date_time',
            'customer_display_name',
            'customer_email',
            'customer_phone',
            'special_requests',
            'total_cost',
            'cost_breakdown',
            'utility_total',
            'utilities',
            'created_at',
            'confirmed_at',
            'cancelled_at',
            'edit_url',
            'cancel_url',
            'book_same_field_url',
        ]);
    }

    #[Test]
    public function get_cost_estimate_returns_valid_estimate()
    {
        // Act
        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.cost-estimate'), [
                'field_id' => $this->field->id,
                'start_time' => '10:00',
                'end_time' => '12:00',
                'utilities' => [
                    ['id' => $this->utility->id, 'hours' => 1],
                ],
            ]);

        // Assert
        $response->assertOk();
        $response->assertJsonStructure([
            'field_name',
            'hourly_rate',
            'duration_hours',
            'total_cost',
            'field_cost',
            'utility_cost',
            'utility_breakdown',
            'formatted',
            'peak_hour_info',
        ]);
    }

    #[Test]
    public function get_cost_estimate_validates_required_fields()
    {
        // Act
        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.cost-estimate'), []);

        // Assert
        $response->assertOk();
        $response->assertJson(['error' => 'Invalid input']);
    }

    #[Test]
    public function get_cost_estimate_validates_field_exists()
    {
        // Act
        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.cost-estimate'), [
                'field_id' => 999,
                'start_time' => '10:00',
                'end_time' => '12:00',
            ]);

        // Assert
        $response->assertOk();
        $response->assertJson(['error' => 'Invalid input']);
    }

    #[Test]
    public function get_cost_estimate_validates_time_format()
    {
        // Act
        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.cost-estimate'), [
                'field_id' => $this->field->id,
                'start_time' => 'invalid',
                'end_time' => '12:00',
            ]);

        // Assert
        $response->assertOk();
        $response->assertJson(['error' => 'Invalid input']);
    }

    #[Test]
    public function get_cost_estimate_validates_utility_exists()
    {
        // Act
        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.cost-estimate'), [
                'field_id' => $this->field->id,
                'start_time' => '10:00',
                'end_time' => '12:00',
                'utilities' => [
                    ['id' => 999, 'hours' => 1],
                ],
            ]);

        // Assert
        $response->assertOk();
        $response->assertJson(['error' => 'Invalid input']);
    }

    #[Test]
    public function check_availability_validates_required_fields()
    {
        // Act
        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.check-availability'), []);

        // Assert
        $response->assertOk();
        $response->assertJson([
            'available' => false,
            'message' => 'Invalid input',
            'slots' => [],
        ]);
    }

    #[Test]
    public function check_availability_validates_field_exists()
    {
        // Act
        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.check-availability'), [
                'field_id' => 999,
                'date' => now()->addDay()->format('Y-m-d'),
                'start_time' => '10:00',
                'end_time' => '12:00',
            ]);

        // Assert
        $response->assertOk();
        $response->assertJson([
            'available' => false,
            'message' => 'Invalid input',
            'slots' => [],
        ]);
    }

    #[Test]
    public function check_availability_validates_duration_range()
    {
        // Act - Test minimum duration (15 minutes = 0.25 hours)
        $responseMin = $this->actingAs($this->user)
            ->postJson(route('reservations.check-availability'), [
                'field_id' => $this->field->id,
                'date' => now()->addDay()->format('Y-m-d'),
                'start_time' => '10:00',
                'end_time' => '10:15', // Below minimum
            ]);

        // Act - Test maximum duration (10 hours)
        $responseMax = $this->actingAs($this->user)
            ->postJson(route('reservations.check-availability'), [
                'field_id' => $this->field->id,
                'date' => now()->addDay()->format('Y-m-d'),
                'start_time' => '10:00',
                'end_time' => '20:00', // Above maximum
            ]);

        // Assert
        $responseMin->assertOk();
        $responseMin->assertJson(['available' => false, 'message' => 'Invalid input']);

        $responseMax->assertOk();
        $responseMax->assertJson(['available' => false, 'message' => 'Invalid input']);
    }

    // ========================================
    // ADDITIONAL VALIDATION TESTS
    // ========================================

    #[Test]
    public function store_validates_working_hours()
    {
        // Arrange - Set field working hours
        $this->field->update([
            'opening_time' => '09:00',
            'closing_time' => '18:00',
        ]);

        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '08:00', // Before opening time
            'end_time' => '10:00',
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        // Assert
        $response->assertSessionHasErrors(['start_time']);
        $response->assertSessionHasErrorsIn('default', [
            'start_time' => 'Reservation must be within field working hours (09:00 - 18:00)',
        ]);
    }

    #[Test]
    public function store_validates_working_hours_with_json_request()
    {
        // Arrange
        $this->field->update([
            'opening_time' => '09:00',
            'closing_time' => '18:00',
        ]);

        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '19:00', // After closing time
            'end_time' => '21:00',
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.store'), $reservationData);

        // Assert
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['start_time']);
    }

    #[Test]
    public function store_validates_field_duration_limits()
    {
        // Arrange
        $this->field->update([
            'min_booking_hours' => 2,
            'max_booking_hours' => 4,
        ]);

        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '11:00', // Below minimum (1 hour when minimum is 2)
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        // Assert
        $response->assertSessionHasErrors(['end_time']);
    }

    #[Test]
    public function store_validates_five_minute_booking_restriction()
    {
        // Arrange - Create reservation starting in 3 minutes, but ensure it's within working hours
        $now = now();

        // If current time is outside working hours (8:00-22:00), use a time within working hours
        if ($now->hour < 8) {
            // If before 8 AM, set to 10:03 AM today (3 minutes from 10:00)
            $futureTime = $now->copy()->setTime(10, 3);
            // Set "now" to 10:00 AM for the test context
            $this->travelTo($now->copy()->setTime(10, 0));
        } elseif ($now->hour >= 22) {
            // If after 10 PM, set to 10:03 AM tomorrow (3 minutes from 10:00)
            $futureTime = $now->copy()->addDay()->setTime(10, 3);
            // Set "now" to 10:00 AM tomorrow for the test context
            $this->travelTo($now->copy()->addDay()->setTime(10, 0));
        } else {
            // Current time is within working hours, just add 3 minutes
            $futureTime = $now->copy()->addMinutes(3);
        }

        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => $futureTime->format('Y-m-d'),
            'start_time' => $futureTime->format('H:i'),
            'end_time' => $futureTime->copy()->addHour()->format('H:i'),
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        // Assert
        $response->assertSessionHasErrors(['start_time']);
        $response->assertSessionHasErrorsIn('default', [
            'start_time' => 'You must book at least 1 hour before the reservation start time.',
        ]);
    }

    #[Test]
    public function store_validates_five_minute_booking_restriction_with_json()
    {
        // Arrange
        $futureTime = now()->addMinutes(2);
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => $futureTime->format('Y-m-d'),
            'start_time' => $futureTime->format('H:i'),
            'end_time' => $futureTime->copy()->addHour()->format('H:i'),
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.store'), $reservationData);

        // Assert
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['start_time']);
    }

    #[Test]
    public function store_validates_field_availability()
    {
        // Arrange - Create existing reservation
        Reservation::factory()->create([
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
        ]);

        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '11:00', // Conflicts with existing reservation
            'end_time' => '13:00',
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        // Assert
        $response->assertSessionHasErrors(['start_time']);
        $response->assertSessionHasErrorsIn('default', [
            'start_time' => 'The selected time slot is not available. Please choose a different time.',
        ]);
    }

    #[Test]
    public function store_validates_vacation_or_maintenance_period()
    {
        // Arrange - Set field to have vacation/maintenance period
        $maintenanceStart = now()->addDays(5);
        $maintenanceEnd = now()->addDays(10);

        $this->field->update([
            'start_date' => $maintenanceStart->format('Y-m-d'),
            'end_date' => $maintenanceEnd->format('Y-m-d'),
        ]);

        // Try to book a date within the maintenance period
        $maintenanceDate = $maintenanceStart->copy()->addDays(2);
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => $maintenanceDate->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00',
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        // Assert
        $response->assertSessionHasErrors(['booking_date']);
        $response->assertSessionHasErrorsIn('default', [
            'booking_date' => 'The reservation date must be outside the vacation or maintenance period ('.
                           $this->field->start_date.' - '.$this->field->end_date.')',
        ]);
    }

    #[Test]
    public function store_validates_vacation_or_maintenance_period_with_json()
    {
        // Arrange - Set field to have vacation/maintenance period
        $maintenanceStart = now()->addDays(5);
        $maintenanceEnd = now()->addDays(10);

        $this->field->update([
            'start_date' => $maintenanceStart->format('Y-m-d'),
            'end_date' => $maintenanceEnd->format('Y-m-d'),
        ]);

        // Try to book a date within the maintenance period
        $maintenanceDate = $maintenanceStart->copy()->addDays(2);
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => $maintenanceDate->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00',
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.store'), $reservationData);

        // Assert
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['booking_date']);
    }

    #[Test]
    public function store_allows_booking_outside_vacation_or_maintenance_period()
    {
        // Arrange - Set field to have vacation/maintenance period
        $maintenanceStart = now()->addDays(10);
        $maintenanceEnd = now()->addDays(15);

        $this->field->update([
            'start_date' => $maintenanceStart->format('Y-m-d'),
            'end_date' => $maintenanceEnd->format('Y-m-d'),
        ]);

        // Book a date outside the maintenance period
        $validDate = now()->addDays(5); // Before maintenance period
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => $validDate->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00',
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        // Assert
        $response->assertRedirect();
        $this->assertDatabaseHas('bookings', [
            'field_id' => $this->field->id,
            'booking_date' => $validDate->format('Y-m-d').' 00:00:00',
            'start_time' => '10:00',
            'end_time' => '12:00',
        ]);
    }

    #[Test]
    public function store_allows_booking_when_no_vacation_or_maintenance_period_set()
    {
        // Arrange - Ensure field has no maintenance period
        $this->field->update([
            'start_date' => null,
            'end_date' => null,
        ]);

        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(5)->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00',
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        // Assert
        $response->assertRedirect();
        $this->assertDatabaseHas('bookings', [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(5)->format('Y-m-d').' 00:00:00',
            'start_time' => '10:00',
            'end_time' => '12:00',
        ]);
    }

    #[Test]
    public function store_creates_reservation_with_pending_status()
    {
        // Arrange
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00',
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        // Assert
        $response->assertRedirect();
        $this->assertDatabaseHas('bookings', [
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'status' => 'Confirmed', // Auto-confirmed in Phase 1
        ]);
    }

    #[Test]
    public function store_calculates_total_cost_server_side()
    {
        // Arrange
        $this->field->update(['hourly_rate' => 50.00]);
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00',
            'utilities' => [
                ['id' => $this->utility->id, 'hours' => 1],
            ],
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        // Assert
        $response->assertRedirect();
        $reservation = Reservation::where('user_id', $this->user->id)->first();
        $this->assertNotNull($reservation->total_cost);
        $this->assertGreaterThan(100, $reservation->total_cost); // Field cost + utility cost
    }

    // ========================================
    // CREATE METHOD ADDITIONAL TESTS
    // ========================================

    #[Test]
    public function create_handles_date_parameter_variations()
    {
        // Test with 'date' parameter
        $response1 = $this->actingAs($this->user)
            ->get(route('reservations.create', ['date' => '2024-12-25']));

        // Test with 'booking_date' parameter
        $response2 = $this->actingAs($this->user)
            ->get(route('reservations.create', ['booking_date' => '2024-12-26']));

        // Test with no date parameter (should default to tomorrow)
        $response3 = $this->actingAs($this->user)
            ->get(route('reservations.create'));

        // Assert all responses are successful
        $response1->assertStatus(200);
        $response2->assertStatus(200);
        $response3->assertStatus(200);
    }

    #[Test]
    public function create_handles_time_parameter_variations()
    {
        // Test with 'time' parameter
        $response1 = $this->actingAs($this->user)
            ->get(route('reservations.create', ['time' => '14:00']));

        // Test with 'start_time' parameter
        $response2 = $this->actingAs($this->user)
            ->get(route('reservations.create', ['start_time' => '15:30']));

        // Test with full time format (FullCalendar format)
        $response3 = $this->actingAs($this->user)
            ->get(route('reservations.create', ['time' => '14:00:00']));

        // Assert all responses are successful
        $response1->assertStatus(200);
        $response2->assertStatus(200);
        $response3->assertStatus(200);
    }

    #[Test]
    public function create_includes_field_availability_when_field_selected()
    {
        // Act
        $response = $this->actingAs($this->user)
            ->get(route('reservations.create', ['field_id' => $this->field->id]));

        // Assert
        $response->assertStatus(200);
        $response->assertViewHas('fieldAvailability');
        $fieldAvailability = $response->viewData('fieldAvailability');
        $this->assertIsArray($fieldAvailability);
    }

    #[Test]
    public function create_sets_default_duration()
    {
        // Act
        $response = $this->actingAs($this->user)
            ->get(route('reservations.create'));

        // Assert
        $response->assertStatus(200);
        $response->assertViewHas('selectedDuration', 1);
    }

    #[Test]
    public function create_respects_custom_duration()
    {
        // Act
        $response = $this->actingAs($this->user)
            ->get(route('reservations.create', ['duration_hours' => 3]));

        // Assert
        $response->assertStatus(200);
        $response->assertViewHas('selectedDuration', 3);
    }

    // ========================================
    // ADMIN AUTHORIZATION TESTS
    // ========================================

    #[Test]
    public function show_allows_admin_to_view_any_reservation()
    {
        // Arrange
        $admin = User::factory()->create(['role' => 'admin']);
        $otherUser = User::factory()->create();
        $reservation = Reservation::factory()->forUser($otherUser)->create();

        // Act
        $response = $this->actingAs($admin)
            ->get(route('reservations.show', $reservation));

        // Assert
        $response->assertStatus(200);
        $response->assertViewHas('reservation', $reservation);
    }

    #[Test]
    public function show_allows_admin_to_view_any_reservation_via_ajax()
    {
        // Arrange
        $admin = User::factory()->create(['role' => 'admin']);
        $otherUser = User::factory()->create();
        $reservation = Reservation::factory()->forUser($otherUser)->create();

        // Act
        $response = $this->actingAs($admin)
            ->getJson(route('reservations.show', $reservation));

        // Assert
        $response->assertOk();
        $response->assertJsonFragment(['id' => $reservation->id]);
    }

    // ========================================
    // UPDATE METHOD ADDITIONAL TESTS
    // ========================================

    #[Test]
    public function update_validates_working_hours()
    {
        // Arrange
        $this->field->update([
            'opening_time' => '09:00',
            'closing_time' => '18:00',
        ]);

        $reservation = Reservation::factory()->forUser($this->user)->create([
            'booking_date' => now()->addDays(3)->format('Y-m-d'),
            'status' => 'Confirmed',
        ]);

        $updateData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(4)->format('Y-m-d'),
            'start_time' => '08:00', // Before opening time
            'end_time' => '10:00',
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->put(route('reservations.update', $reservation), $updateData);

        // Assert
        $response->assertSessionHasErrors(['start_time']);
    }

    #[Test]
    public function update_validates_duration_limits()
    {
        // Arrange
        $this->field->update([
            'min_booking_hours' => 2,
            'max_booking_hours' => 4,
        ]);

        $reservation = Reservation::factory()->forUser($this->user)->create([
            'booking_date' => now()->addDays(3)->format('Y-m-d'),
            'status' => 'Confirmed',
        ]);

        $updateData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(4)->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '15:00', // Above maximum (5 hours when max is 4)
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->put(route('reservations.update', $reservation), $updateData);

        // Assert
        $response->assertSessionHasErrors(['end_time']);
    }

    #[Test]
    public function update_validates_availability_excluding_current_reservation()
    {
        // Arrange
        $reservation = Reservation::factory()->forUser($this->user)->create([
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(3)->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
        ]);

        // Create another reservation that would conflict
        Reservation::factory()->create([
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(3)->format('Y-m-d'),
            'start_time' => '14:00',
            'end_time' => '16:00',
            'status' => 'Confirmed',
        ]);

        $updateData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(3)->format('Y-m-d'),
            'start_time' => '15:00', // Would conflict with other reservation
            'end_time' => '17:00',
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->put(route('reservations.update', $reservation), $updateData);

        // Assert
        $response->assertSessionHasErrors(['start_time']);
    }

    // ========================================
    // EDGE CASES AND ERROR CONDITIONS
    // ========================================

    #[Test]
    public function store_handles_half_hour_increments()
    {
        // Arrange
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '11:30', // Half-hour increment
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        // Assert
        $response->assertRedirect();
        $this->assertDatabaseHas('bookings', [
            'field_id' => $this->field->id,
            'start_time' => '10:00',
            'end_time' => '11:30',
            'duration_hours' => 1.5,
        ]);
    }

    #[Test]
    public function store_rejects_invalid_duration_increments()
    {
        // Arrange
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '11:15', // Invalid increment (1.25 hours, not .0 or .5)
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        // Assert
        $response->assertSessionHasErrors(['end_time']);
    }

    #[Test]
    public function update_handles_half_hour_increments()
    {
        // Arrange
        $reservation = Reservation::factory()->forUser($this->user)->create([
            'booking_date' => now()->addDays(3)->format('Y-m-d'),
            'status' => 'Confirmed',
        ]);

        $updateData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(4)->format('Y-m-d'),
            'start_time' => '14:00',
            'end_time' => '16:30', // Half-hour increment
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->put(route('reservations.update', $reservation), $updateData);

        // Assert
        $response->assertRedirect();
        $this->assertDatabaseHas('bookings', [
            'id' => $reservation->id,
            'start_time' => '14:00',
            'end_time' => '16:30',
            'duration_hours' => 2.5,
        ]);
    }

    #[Test]
    public function store_uses_authenticated_user_defaults_for_customer_info()
    {
        // Arrange
        $this->user->update([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
        ]);

        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00',
            // No customer info provided
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        // Assert
        $response->assertRedirect();
        $this->assertDatabaseHas('bookings', [
            'customer_name' => 'John Doe',
            'customer_email' => null, // Current implementation doesn't auto-fill email
        ]);
    }

    #[Test]
    public function update_validates_sixty_minute_advance_booking_restriction()
    {
        // Arrange - Create reservation that can be modified (more than 24 hours ahead)
        $reservation = Reservation::factory()->forUser($this->user)->create([
            'booking_date' => now()->addDays(3)->format('Y-m-d'),
            'status' => 'Confirmed',
        ]);

        // Try to update to a time that's less than 60 minutes from now, but ensure it's within working hours
        $now = now();

        // If current time is outside working hours (8:00-22:00), use a time within working hours
        if ($now->hour < 8) {
            // If before 8 AM, set to 10:30 AM today (30 minutes from 10:00)
            $futureTime = $now->copy()->setTime(10, 30);
            // Set "now" to 10:00 AM for the test context
            $this->travelTo($now->copy()->setTime(10, 0));
        } elseif ($now->hour >= 22) {
            // If after 10 PM, set to 10:30 AM tomorrow (30 minutes from 10:00)
            $futureTime = $now->copy()->addDay()->setTime(10, 30);
            // Set "now" to 10:00 AM tomorrow for the test context
            $this->travelTo($now->copy()->addDay()->setTime(10, 0));
        } else {
            // Current time is within working hours, just add 30 minutes
            $futureTime = $now->copy()->addMinutes(30);
        }

        $updateData = [
            'field_id' => $this->field->id,
            'booking_date' => $futureTime->format('Y-m-d'),
            'start_time' => $futureTime->format('H:i'),
            'end_time' => $futureTime->copy()->addHour()->format('H:i'),
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->put(route('reservations.update', $reservation), $updateData);

        // Assert
        $response->assertSessionHasErrors(['start_time']);
        $response->assertSessionHasErrorsIn('default', [
            'start_time' => 'You must book at least 1 hour before the reservation start time.',
        ]);
    }

    #[Test]
    public function update_validates_vacation_or_maintenance_period()
    {
        // Arrange - Set field to have vacation/maintenance period
        $maintenanceStart = now()->addDays(5);
        $maintenanceEnd = now()->addDays(10);

        $this->field->update([
            'start_date' => $maintenanceStart->format('Y-m-d'),
            'end_date' => $maintenanceEnd->format('Y-m-d'),
        ]);

        $reservation = Reservation::factory()->forUser($this->user)->create([
            'booking_date' => now()->addDays(3)->format('Y-m-d'),
            'status' => 'Confirmed',
        ]);

        // Try to update to a date within the maintenance period
        $maintenanceDate = $maintenanceStart->copy()->addDays(2);
        $updateData = [
            'field_id' => $this->field->id,
            'booking_date' => $maintenanceDate->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00',
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->put(route('reservations.update', $reservation), $updateData);

        // Assert
        $response->assertSessionHasErrors(['booking_date']);
        $response->assertSessionHasErrorsIn('default', [
            'booking_date' => 'The reservation date must be outside the vacation or maintenance period ('.
                           $this->field->start_date.' - '.$this->field->end_date.')',
        ]);
    }

    #[Test]
    public function update_allows_booking_outside_vacation_or_maintenance_period()
    {
        // Arrange - Set field to have vacation/maintenance period
        $maintenanceStart = now()->addDays(10);
        $maintenanceEnd = now()->addDays(15);

        $this->field->update([
            'start_date' => $maintenanceStart->format('Y-m-d'),
            'end_date' => $maintenanceEnd->format('Y-m-d'),
        ]);

        $reservation = Reservation::factory()->forUser($this->user)->create([
            'booking_date' => now()->addDays(3)->format('Y-m-d'),
            'status' => 'Confirmed',
        ]);

        // Update to a date outside the maintenance period
        $validDate = now()->addDays(5); // Before maintenance period
        $updateData = [
            'field_id' => $this->field->id,
            'booking_date' => $validDate->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00',
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->put(route('reservations.update', $reservation), $updateData);

        // Assert
        $response->assertRedirect(route('reservations.show', $reservation));
        $response->assertSessionHas('success');
    }

    #[Test]
    public function update_allows_booking_when_no_vacation_or_maintenance_period_set()
    {
        // Arrange - Ensure field has no maintenance period
        $this->field->update([
            'start_date' => null,
            'end_date' => null,
        ]);

        $reservation = Reservation::factory()->forUser($this->user)->create([
            'booking_date' => now()->addDays(3)->format('Y-m-d'),
            'status' => 'Confirmed',
        ]);

        $updateData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(5)->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00',
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->put(route('reservations.update', $reservation), $updateData);

        // Assert
        $response->assertRedirect(route('reservations.show', $reservation));
        $response->assertSessionHas('success');
    }

    #[Test]
    public function update_uses_authenticated_user_defaults_for_empty_customer_info()
    {
        // Arrange
        $this->user->update([
            'name' => 'Jane Smith',
            'email' => '<EMAIL>',
        ]);

        $reservation = Reservation::factory()->forUser($this->user)->create([
            'booking_date' => now()->addDays(3)->format('Y-m-d'),
            'status' => 'Confirmed',
        ]);

        $updateData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(4)->format('Y-m-d'),
            'start_time' => '14:00',
            'end_time' => '16:00',
            'customer_name' => '', // Empty string
            'customer_email' => '', // Empty string
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->put(route('reservations.update', $reservation), $updateData);

        // Assert
        $response->assertRedirect();
        $this->assertDatabaseHas('bookings', [
            'id' => $reservation->id,
            'customer_name' => 'Jane Smith',
            'customer_email' => null, // Current implementation doesn't auto-fill email
        ]);
    }

    #[Test]
    public function get_cost_estimate_requires_authentication()
    {
        // Act
        $response = $this->postJson(route('reservations.cost-estimate'), [
            'field_id' => $this->field->id,
            'start_time' => '10:00',
            'end_time' => '12:00',
        ]);

        // Assert
        $response->assertStatus(401);
    }

    #[Test]
    public function check_availability_requires_authentication()
    {
        // Act
        $response = $this->postJson(route('reservations.check-availability'), [
            'field_id' => $this->field->id,
            'date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00',
        ]);

        // Assert
        $response->assertStatus(401);
    }

    #[Test]
    public function store_auto_confirms_reservation_in_phase_one()
    {
        // Arrange
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00',
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        // Assert
        $response->assertRedirect();
        $reservation = Reservation::where('user_id', $this->user->id)->first();
        $this->assertEquals('Confirmed', $reservation->status);
        $this->assertNotNull($reservation->confirmed_at);
    }

    #[Test]
    public function update_recalculates_total_cost_with_utilities()
    {
        // Arrange
        $reservation = Reservation::factory()->forUser($this->user)->create([
            'booking_date' => now()->addDays(3)->format('Y-m-d'),
            'status' => 'Confirmed',
            'total_cost' => 100.00,
        ]);

        $updateData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(4)->format('Y-m-d'),
            'start_time' => '14:00',
            'end_time' => '17:00', // Increased duration
            'utilities' => [
                ['id' => $this->utility->id, 'hours' => 2], // Added utilities
            ],
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->put(route('reservations.update', $reservation), $updateData);

        // Assert
        $response->assertRedirect();
        $reservation->refresh();
        $this->assertNotEquals(100.00, $reservation->total_cost);
        $this->assertTrue($reservation->utilities->contains($this->utility));
    }
}
